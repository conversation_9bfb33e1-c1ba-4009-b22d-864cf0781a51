<!-- My Packages Content -->
<div class="max-w-6xl">

  <!-- Loading State -->
  <div *ngIf="packagesLoading" class="flex justify-center items-center py-20">
    <div class="bg-slate-800/60 backdrop-blur-md border border-slate-600/30 rounded-xl p-6">
      <svg class="animate-spin h-8 w-8 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="packagesError && !packagesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 text-center mb-4">
    <p class="text-sm lg:text-base text-red-300 mb-3">{{ packagesError }}</p>
    <button
      (click)="loadMyPackages()"
      class="px-3 py-1.5 text-sm lg:text-base bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- My Packages Content -->
  <div *ngIf="!packagesLoading && !packagesError">
    <!-- Header with Package Icon and Title -->
    <div class="flex items-center mb-4 lg:mb-6">
      <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3 lg:mr-4">
        <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
        </svg>
      </div>
      <div>
        <h1 class="text-xl lg:text-2xl font-bold text-white mb-1">Мои пакеты</h1>
        <p class="text-sm lg:text-base text-gray-300">Управляйте своими пакетами игр и выбирайте игры для добавления в библиотеку</p>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="packages.length === 0" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 lg:p-8 text-center">
      <div class="w-16 h-16 lg:w-20 lg:h-20 bg-slate-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 lg:w-10 lg:h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
        </svg>
      </div>
      <h3 class="text-lg lg:text-xl font-semibold text-white mb-2">Нет активных пакетов</h3>
      <p class="text-sm lg:text-base text-gray-400 mb-4">У вас пока нет приобретенных пакетов игр.</p>
      <a
        href="/#tariffs"
        class="inline-block px-4 py-2 lg:px-6 lg:py-3 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
      >
        Посмотреть пакеты
      </a>
    </div>

    <!-- Packages Grid -->
    <div *ngIf="packages.length > 0" class="space-y-4 lg:space-y-6">
      <div *ngFor="let package of packages" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-4 lg:p-6 profile-section">
        <!-- Package Header -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
          <h3 class="text-base lg:text-lg font-semibold text-white">{{ package.package_name }}</h3>
          <span
            [class]="isPackageExpired(package.expires_at) ? 'bg-red-900/50 text-red-400 border-red-500/50' : 'bg-green-900/50 text-green-400 border-green-500/50'"
            class="inline-flex px-2 py-1 text-xs font-semibold rounded border self-start sm:self-auto"
          >
            {{ isPackageExpired(package.expires_at) ? 'Истек' : 'Активен' }}
          </span>
        </div>

        <!-- Package Info Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Доступно слотов</label>
            <div class="px-3 py-2 lg:py-2.5 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
              {{ package.remaining_slots }}
            </div>
          </div>
          <div>
            <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Выбрано игр</label>
            <div class="px-3 py-2 lg:py-2.5 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
              {{ package.selected_games.length }}
            </div>
          </div>
          <div>
            <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Истекает</label>
            <div class="px-3 py-2 lg:py-2.5 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
              {{ formatExpirationDate(package.expires_at) }}
            </div>
          </div>
        </div>

        <!-- Selected Games -->
        <div *ngIf="package.selected_games.length > 0" class="mb-4">
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-2">Выбранные игры</label>
          <div class="space-y-2">
            <div *ngFor="let game of package.selected_games" class="px-3 py-2 text-sm lg:text-base bg-slate-800/60 border border-slate-600/50 rounded-md text-white">
              {{ game.title }}
            </div>
          </div>
        </div>

        <!-- Action Button -->
        <div class="flex justify-end">
          <button
            *ngIf="package.remaining_slots > 0 && !isPackageExpired(package.expires_at)"
            (click)="openGameSelection(package)"
            class="px-4 lg:px-6 py-2 lg:py-2.5 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
          >
            Выбрать игры ({{ package.remaining_slots }} доступно)
          </button>

          <div
            *ngIf="package.remaining_slots === 0 || isPackageExpired(package.expires_at)"
            class="px-4 lg:px-6 py-2 lg:py-2.5 text-sm lg:text-base bg-gray-600 text-gray-300 rounded-md text-center"
          >
            {{ isPackageExpired(package.expires_at) ? 'Пакет истек' : 'Все игры выбраны' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="getTotalPages() > 1" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-4 lg:p-6 mt-4 lg:mt-6">
      <div class="flex justify-center items-center space-x-2">
        <button
          (click)="onPageChange(currentPage - 1)"
          [disabled]="!hasPrevious"
          class="px-3 py-2 text-sm lg:text-base font-medium text-gray-300 bg-slate-800/60 border border-slate-600/50 rounded-md hover:bg-slate-700/60 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Назад
        </button>

        <div class="flex space-x-1">
          <button
            *ngFor="let page of getPageNumbers()"
            (click)="onPageChange(page)"
            [class]="page === currentPage ? 'bg-blue-600 text-white border-blue-600' : 'bg-slate-800/60 text-gray-300 border-slate-600/50 hover:bg-slate-700/60'"
            class="px-3 py-2 text-sm lg:text-base font-medium border rounded-md transition-colors"
          >
            {{ page }}
          </button>
        </div>

        <button
          (click)="onPageChange(currentPage + 1)"
          [disabled]="!hasNext"
          class="px-3 py-2 text-sm lg:text-base font-medium text-gray-300 bg-slate-800/60 border border-slate-600/50 rounded-md hover:bg-slate-700/60 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Вперед
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Game Selection Modal -->
<div *ngIf="showGameSelection" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" (click)="closeGameSelection()">
  <div class="bg-slate-900/95 backdrop-blur-md border border-slate-700/40 rounded-lg p-4 lg:p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto" (click)="$event.stopPropagation()">
    <!-- Modal Header -->
    <div class="flex items-center justify-between mb-4 lg:mb-6">
      <div class="flex items-center">
        <div class="w-8 h-8 lg:w-10 lg:h-10 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-3">
          <svg class="w-4 h-4 lg:w-5 lg:h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
          </svg>
        </div>
        <h3 class="text-lg lg:text-xl font-semibold text-white">Выберите игры</h3>
      </div>
      <button (click)="closeGameSelection()" class="text-gray-400 hover:text-white transition-colors">
        <svg class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Package Info -->
    <div *ngIf="getCurrentPackage()" class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-4 mb-4 lg:mb-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Пакет</label>
          <div class="px-3 py-2 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
            {{ getCurrentPackage()?.package_name }}
          </div>
        </div>
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Доступно слотов</label>
          <div class="px-3 py-2 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
            {{ getCurrentPackage()?.remaining_slots }}
          </div>
        </div>
        <div>
          <label class="block text-sm lg:text-base font-medium text-gray-300 mb-1">Выбрано</label>
          <div class="px-3 py-2 text-sm lg:text-base bg-slate-700/30 border border-slate-600/30 rounded-md text-white">
            {{ selectedGameIds.length }} / {{ getCurrentPackage()?.remaining_slots }}
          </div>
        </div>
      </div>
    </div>

    <!-- Available Games -->
    <div class="mb-4 lg:mb-6">
      <label class="block text-sm lg:text-base font-medium text-gray-300 mb-3">Доступные игры</label>
      <div class="space-y-2">
        <div
          *ngFor="let game of getCurrentPackage()?.available_games"
          class="flex items-center justify-between p-3 bg-slate-800/60 border border-slate-600/50 rounded-md hover:bg-slate-700/60 transition-colors"
        >
          <span class="text-sm lg:text-base text-white">{{ game.title }}</span>
          <label class="flex items-center cursor-pointer">
            <input
              type="checkbox"
              [checked]="isGameSelected(game.id)"
              (change)="toggleGameSelection(game.id)"
              class="sr-only"
            >
            <div [class]="isGameSelected(game.id) ? 'bg-blue-600 border-blue-600' : 'bg-slate-700 border-slate-600'" class="w-5 h-5 rounded border-2 flex items-center justify-center transition-colors">
              <svg *ngIf="isGameSelected(game.id)" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Modal Actions -->
    <div class="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
      <button
        (click)="closeGameSelection()"
        class="px-4 py-2 lg:px-6 lg:py-2.5 text-sm lg:text-base text-gray-300 bg-slate-800/60 border border-slate-600/50 rounded-md hover:bg-slate-700/60 transition-colors"
      >
        Отмена
      </button>
      <button
        (click)="confirmGameSelection()"
        [disabled]="selectedGameIds.length === 0 || selectingGames"
        class="px-4 py-2 lg:px-6 lg:py-2.5 text-sm lg:text-base bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        <svg *ngIf="selectingGames" class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ selectingGames ? 'Выбираем...' : 'Подтвердить выбор' }}
      </button>
    </div>
  </div>
</div>
